<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Flexbox Nav + Grid Gallery Demo</title>
    <style>
      :root {
        --bg: #f7f9fc;
        --card: #ffffff;
        --muted: #6b7280;
        --accent: #2563eb;
        --gap: 1rem;
        --radius: 10px;
      }

      /* Basic page */
      body {
        margin: 0;
        font-family:
          Inter,
          system-ui,
          -apple-system,
          "Segoe UI",
          Roboto,
          "Helvetica Neue",
          Arial;
        background: var(--bg);
        color: #111827;
        padding: 1.25rem;
        line-height: 1.4;
      }
      h1 {
        font-size: 1.25rem;
        margin: 0 0 1rem 0;
      }

      /* NAV: Flexbox layout */
      .site-header {
        background: var(--card);
        border-radius: var(--radius);
        padding: 0.5rem 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        box-shadow: 0 1px 4px rgba(15, 23, 42, 0.06);
      }

      .brand {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 700;
        color: var(--accent);
      }
      .brand img {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        object-fit: cover;
      }

      /* nav links container - flex grows */
      nav {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-left: 0.5rem;
        flex: 1;
      }

      .nav-links {
        display: flex;
        gap: 0.5rem;
        list-style: none;
        margin: 0;
        padding: 0;
      }
      .nav-links a {
        display: inline-block;
        padding: 0.45rem 0.6rem;
        border-radius: 8px;
        text-decoration: none;
        color: var(--muted);
        font-weight: 600;
      }
      .nav-links a:hover,
      .nav-links a:focus {
        background: rgba(37, 99, 235, 0.08);
        color: var(--accent);
        outline: none;
      }
      /* CTA on the right */
      .nav-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }
      .btn {
        background: var(--accent);
        color: #fff;
        padding: 0.45rem 0.7rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        border: none;
        cursor: pointer;
      }
      .btn:focus {
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.25);
        outline: none;
      }

      /* Responsive: collapse links into hamburger */
      .hamburger {
        display: none;
        background: transparent;
        border: 1px solid transparent;
        padding: 0.35rem;
        border-radius: 8px;
        cursor: pointer;
      }
      .hamburger:focus {
        box-shadow: 0 0 0 3px rgba(15, 23, 42, 0.06);
        outline: none;
      }

      /* MOBILE styles */
      @media (max-width: 800px) {
        .nav-links {
          display: none;
          flex-direction: column;
          gap: 0.25rem;
          width: 100%;
        }
        .nav-links.show {
          display: flex;
        }
        nav {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;
          margin-left: 0;
        }
        .hamburger {
          display: inline-flex;
          align-items: center;
          gap: 0.35rem;
        }
        .nav-actions {
          order: 3;
        }
      }

      /* Gallery header */
      .controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 1rem 0;
        gap: 1rem;
      }
      .muted {
        color: var(--muted);
        font-size: 0.95rem;
      }

      /* Photo gallery: CSS Grid */
      .gallery {
        display: grid;
        gap: 0.75rem;
        /* responsive columns: as many 220px columns as fit */
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      }
      .photo-card {
        background: var(--card);
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(15, 23, 42, 0.06);
        display: flex;
        flex-direction: column;
      }
      .photo-card img {
        width: 100%;
        height: 160px;
        object-fit: cover;
        display: block;
      }
      .photo-meta {
        padding: 0.6rem;
        font-size: 0.95rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 0.5rem;
      }
      .photo-meta .title {
        font-weight: 600;
        color: #111827;
      }
      .photo-meta .small {
        color: var(--muted);
        font-size: 0.85rem;
      }

      /* focus style for images (keyboard users) */
      .photo-card a {
        display: block;
        text-decoration: none;
        color: inherit;
      }
      .photo-card a:focus-visible {
        box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.18);
        outline: none;
        border-radius: 10px;
      }

      /* small utility */
      .wrap {
        max-width: 1100px;
        margin: 0 auto;
      }
    </style>
  </head>
  <body>
    <div class="wrap">
      <header class="site-header" role="banner" aria-label="Main header">
        <div class="brand" aria-hidden="false">
          <img src="https://picsum.photos/seed/logo/64/64" alt="logo" />
          <span>DemoSite</span>
        </div>

        <nav role="navigation" aria-label="Primary navigation">
          <!-- hamburger for small screens -->
          <button
            class="hamburger"
            aria-expanded="false"
            aria-controls="primary-navigation"
            id="hamburger"
          >
            <svg width="18" height="12" viewBox="0 0 18 12" aria-hidden="true">
              <rect width="18" height="2" y="0"></rect>
              <rect width="18" height="2" y="5"></rect>
              <rect width="18" height="2" y="10"></rect>
            </svg>
          </button>

          <ul class="nav-links" id="primary-navigation">
            <li><a href="#home">Home</a></li>
            <li><a href="#gallery">Gallery</a></li>
            <li><a href="#about">About</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>

          <div class="nav-actions" aria-hidden="false"></div>
        </nav>
      </header>
    </div>
  </body>
</html>
