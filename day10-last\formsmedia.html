<!-- Internal CSS and HTML with media, forms, and accessibility -->

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">      
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Accessible Contact Form</title>
<style>
    body {
        font-family: Arial, sans-serif;
        padding: 2rem;
        background-color: #f4f4f4;
    }
    h1 {
        color: #333;
    }
    form {
        background: #fff;
        padding: 2rem;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        max-width: 600px;
        margin: auto;
    }
    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }
    input, textarea {
        width: 100%;
        padding: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #ccc;
        border-radius: 3px;
    }
    button {
        padding: 0.7rem 1.5rem;
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }
    button:hover {
        background-color: #218838;
    }
</style>
</head>
    <h1>Contact Us</h1>
    <form action="#" method="post">    
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required>
 
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required>
 
        <label for="message">Message:</label>
        <textarea id="message" name="message" rows="5" required></textarea>
 
        <button type="submit">Submit</button>
    </form>
</body>
<footer>
    <p>&copy; 2024 Accessible Contact Form. All rights reserved.</p>
</footer>
</html>
 