<!-- Internal CSS and HTML with !important and inline styles  -->

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Important & Inline Demo</title>
    <style>
      .card {
        color: black;
      }
      /* author styles can use !important to override most rules */
      .card {
        color: purple !important;
      }
    </style>
  </head>
  <body>
    <h3>!important and inline styles</h3>

    <!-- Inline style has high priority (acts like specificity of inline) -->
    <p class="card" style="color: crimson">
      I am crimson (inline style wins over normal rules).
    </p>

    <!-- But !important on stylesheet still wins over non-!important inline? -->
    <p class="card" style="color: teal !important">
      I am teal (inline + !important — strongest of these examples).
    </p>

    <!-- If stylesheet has !important, it will override normal inline -->
    <p class="card">
      I am purple because stylesheet used <code>!important</code>.
    </p>
  </body>
</html>
