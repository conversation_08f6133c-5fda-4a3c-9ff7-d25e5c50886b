<!-- Internal CSS and HTML with box sizing -->

<!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <title>Box-Sizing</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          padding: 2rem;
        }

        h2 {
          margin-top: 0;
        }

        .demo-container {
          display: flex;
          gap: 2rem;
        }
        .box {
          width: 200px;
          padding: 20px;
          border: 5px solid black;
          background-color: lightblue;
        }
        .content-box {
          box-sizing: content-box; /* Default value */
        }
        .border-box {
          box-sizing: border-box;
        }
      </style>
    </head>
    <body>
      <h1>Box-Sizing Example</h1>
      <p>
        This example demonstrates the difference between
        <code>content-box</code> and <code>border-box</code>.
      </p>

      <div class="box content-box">
        <h2>Content-Box</h2>
        <p>
          This box uses <code>box-sizing: content-box;</code>. The width and
          height apply to the content only.
        </p>
      </div>

      <div class="box border-box">
        <h2>Border-Box</h2>
        <p>
          This box uses <code>box-sizing: border-box;</code>. The width and
          height include padding and border.
        </p>
      </div>
    </body>
  </html>
</html>
