<!-- Internal CSS and HTML with integrate html and css -->
<!-- ID vs class -->

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Specificity Demo - ID vs Class</title>
    <style>
      /* class selector (specificity: 0,0,1,0) */
      .btn {
        background: lightcoral;
        color: white;
        padding: 8px 12px;
        border: none;
      }

      /* ID selector (specificity: 0,1,0,0) - higher specificity, wins */
      #special .btn {
        background: royalblue;
      }
    </style>
  </head>
  <body>
    <h3>Specificity: ID wins over class</h3>
    <div id="special">
      <button class="btn">I am blue (ID wins)</button>
    </div>
    
    <div>
      <button class="btn">I am coral (no ID)</button>
    </div>
  </body>
</html>
